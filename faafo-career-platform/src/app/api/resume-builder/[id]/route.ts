import { NextResponse, NextRequest } from 'next/server';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/lib/auth';
import prisma from '@/lib/prisma';
import { ErrorReporter } from '@/lib/errorReporting';
import { log } from '@/lib/logger';
import { trackError } from '@/lib/errorTracking';
import { withCSRFProtection } from '@/lib/csrf';
import { withRateLimit } from '@/lib/rateLimit';
import { z } from 'zod';

// Validation schemas with proper length limits (same as in main route)
const personalInfoSchema = z.object({
  firstName: z.string()
    .min(1, 'First name is required')
    .max(50, 'First name must be less than 50 characters')
    .regex(/^[a-zA-Z\s'-]+$/, 'First name can only contain letters, spaces, hyphens, and apostrophes'),
  lastName: z.string()
    .min(1, 'Last name is required')
    .max(50, 'Last name must be less than 50 characters')
    .regex(/^[a-zA-Z\s'-]+$/, 'Last name can only contain letters, spaces, hyphens, and apostrophes'),
  email: z.string()
    .email('Valid email is required')
    .max(254, 'Email is too long'),
  phone: z.string()
    .max(20, 'Phone number is too long')
    .regex(/^[\+]?[1-9][\d\s\-\(\)]{0,15}$/, 'Please enter a valid phone number')
    .optional()
    .or(z.literal('')),
  location: z.string()
    .max(100, 'Location must be less than 100 characters')
    .optional(),
  website: z.string()
    .url('Please enter a valid website URL')
    .max(500, 'Website URL is too long')
    .optional()
    .or(z.literal('')),
  linkedIn: z.string()
    .url('Please enter a valid LinkedIn URL')
    .max(500, 'LinkedIn URL is too long')
    .optional()
    .or(z.literal(''))
});

const experienceSchema = z.object({
  company: z.string().min(1, 'Company name is required'),
  position: z.string().min(1, 'Position is required'),
  startDate: z.string().min(1, 'Start date is required'),
  endDate: z.string().optional(),
  description: z.string().optional(),
  achievements: z.array(z.string()).optional()
});

const educationSchema = z.object({
  institution: z.string().min(1, 'Institution is required'),
  degree: z.string().min(1, 'Degree is required'),
  field: z.string().optional(),
  startDate: z.string().optional(),
  endDate: z.string().optional(),
  gpa: z.string().optional(),
  honors: z.string().optional()
});

const skillSchema = z.object({
  name: z.string().min(1, 'Skill name is required'),
  level: z.enum(['BEGINNER', 'INTERMEDIATE', 'ADVANCED', 'EXPERT']).optional(),
  category: z.string().optional()
});

const resumeUpdateSchema = z.object({
  title: z.string().min(1, 'Resume title is required').optional(),
  personalInfo: personalInfoSchema.optional(),
  summary: z.string().optional(),
  experience: z.array(experienceSchema).optional(),
  education: z.array(educationSchema).optional(),
  skills: z.array(skillSchema).optional(),
  sections: z.record(z.any()).optional(),
  template: z.string().optional(),
  isPublic: z.boolean().optional()
});

// GET - Retrieve specific resume
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  return withRateLimit(
    request,
    { windowMs: 15 * 60 * 1000, maxRequests: 200 }, // 200 requests per 15 minutes
    async () => {
      const startTime = Date.now();
      const session = await getServerSession(authOptions);
      const { id } = await params;

      if (!session?.user?.email) {
        log.auth('resume_access_denied', undefined, false, {
          component: 'resume_builder_api',
          action: 'get_resume'
        });
        return NextResponse.json({ 
          success: false,
          error: 'Not authenticated' 
        }, { status: 401 });
      }

      try {
        log.info('Fetching resume', {
          component: 'resume_builder_api',
          action: 'get_resume',
          userId: session.user.email
        });

        const dbStartTime = Date.now();
        const user = await prisma.user.findUnique({
          where: { email: session.user.email },
          select: { id: true }
        });

        if (!user) {
          return NextResponse.json({ 
            success: false,
            error: 'User not found' 
          }, { status: 404 });
        }

        const resume = await prisma.resume.findFirst({
          where: { 
            id,
            userId: user.id,
            isActive: true
          }
        });

        const dbDuration = Date.now() - dbStartTime;
        log.database('findFirst', 'resume', dbDuration, {
          userId: user.id
        });

        if (!resume) {
          return NextResponse.json({ 
            success: false,
            error: 'Resume not found' 
          }, { status: 404 });
        }

        const totalDuration = Date.now() - startTime;
        log.api('GET', `/api/resume-builder/${id}`, 200, totalDuration, {
          component: 'resume_builder_api',
          userId: session.user.email
        });

        return NextResponse.json({
          success: true,
          data: resume
        });

      } catch (error) {
        const totalDuration = Date.now() - startTime;

        log.error('Error fetching resume', error as Error, {
          component: 'resume_builder_api',
          action: 'get_resume',
          userId: session.user.email
        });

        trackError.api(error as Error, `/api/resume-builder/${id}`, 'GET', 500);

        ErrorReporter.captureError(error as Error, {
          userId: session.user.email,
          userEmail: session.user.email,
          action: 'get_resume',
          component: 'resume_builder_api'
        });

        log.api('GET', `/api/resume-builder/${id}`, 500, totalDuration, {
          component: 'resume_builder_api',
          userId: session.user.email,
          metadata: { hasError: true }
        });

        return NextResponse.json({ 
          success: false,
          error: 'Internal Server Error' 
        }, { status: 500 });
      }
    }
  );
}

// PUT - Update resume
export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  return withCSRFProtection(request, async () => {
    return withRateLimit(
      request,
      { windowMs: 15 * 60 * 1000, maxRequests: 50 }, // 50 updates per 15 minutes
      async () => {
        const startTime = Date.now();
        const session = await getServerSession(authOptions);
        const { id } = await params;

        if (!session?.user?.email) {
          return NextResponse.json({ 
            success: false,
            error: 'Not authenticated' 
          }, { status: 401 });
        }

        try {
          const user = await prisma.user.findUnique({
            where: { email: session.user.email },
            select: { id: true }
          });

          if (!user) {
            return NextResponse.json({ 
              success: false,
              error: 'User not found' 
            }, { status: 404 });
          }

          // Verify resume ownership
          const existingResume = await prisma.resume.findFirst({
            where: { 
              id,
              userId: user.id,
              isActive: true
            }
          });

          if (!existingResume) {
            return NextResponse.json({ 
              success: false,
              error: 'Resume not found' 
            }, { status: 404 });
          }

          const body = await request.json();
          const validatedData = resumeUpdateSchema.parse(body);

          log.info('Updating resume', {
            component: 'resume_builder_api',
            action: 'update_resume',
            userId: user.id
          });

          const dbStartTime = Date.now();
          const updatedResume = await prisma.resume.update({
            where: { id },
            data: {
              ...validatedData,
              updatedAt: new Date()
            }
          });

          const dbDuration = Date.now() - dbStartTime;
          log.database('update', 'resume', dbDuration, {
            userId: user.id
          });

          const totalDuration = Date.now() - startTime;
          log.api('PUT', `/api/resume-builder/${id}`, 200, totalDuration, {
            component: 'resume_builder_api',
            userId: session.user.email
          });

          return NextResponse.json({
            success: true,
            data: updatedResume
          });

        } catch (error) {
          const totalDuration = Date.now() - startTime;

          if (error instanceof z.ZodError) {
            log.warn('Resume update validation failed', {
              component: 'resume_builder_api',
              action: 'update_resume',
              userId: session.user.email
            });

            return NextResponse.json({
              success: false,
              error: 'Validation failed',
              details: error.errors
            }, { status: 400 });
          }

          log.error('Error updating resume', error as Error, {
            component: 'resume_builder_api',
            action: 'update_resume',
            userId: session.user.email
          });

          trackError.api(error as Error, `/api/resume-builder/${id}`, 'PUT', 500);

          ErrorReporter.captureError(error as Error, {
            userId: session.user.email,
            userEmail: session.user.email,
            action: 'update_resume',
            component: 'resume_builder_api'
          });

          log.api('PUT', `/api/resume-builder/${id}`, 500, totalDuration, {
            component: 'resume_builder_api',
            userId: session.user.email
          });

          return NextResponse.json({
            success: false,
            error: 'Internal Server Error'
          }, { status: 500 });
        }
      }
    );
  });
}

// DELETE - Delete resume (soft delete)
export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  return withCSRFProtection(request, async () => {
    return withRateLimit(
      request,
      { windowMs: 15 * 60 * 1000, maxRequests: 20 }, // 20 deletes per 15 minutes
      async () => {
        const startTime = Date.now();
        const session = await getServerSession(authOptions);
        const { id } = await params;

        if (!session?.user?.email) {
          return NextResponse.json({
            success: false,
            error: 'Not authenticated'
          }, { status: 401 });
        }

        try {
          const user = await prisma.user.findUnique({
            where: { email: session.user.email },
            select: { id: true }
          });

          if (!user) {
            return NextResponse.json({
              success: false,
              error: 'User not found'
            }, { status: 404 });
          }

          // Verify resume ownership
          const existingResume = await prisma.resume.findFirst({
            where: {
              id,
              userId: user.id,
              isActive: true
            }
          });

          if (!existingResume) {
            return NextResponse.json({
              success: false,
              error: 'Resume not found'
            }, { status: 404 });
          }

          log.info('Deleting resume', {
            component: 'resume_builder_api',
            action: 'delete_resume',
            userId: user.id
          });

          const dbStartTime = Date.now();
          // Soft delete by setting isActive to false
          await prisma.resume.update({
            where: { id },
            data: {
              isActive: false,
              updatedAt: new Date()
            }
          });

          const dbDuration = Date.now() - dbStartTime;
          log.database('update', 'resume', dbDuration, {
            userId: user.id,
            action: 'soft_delete'
          });

          const totalDuration = Date.now() - startTime;
          log.api('DELETE', `/api/resume-builder/${id}`, 200, totalDuration, {
            component: 'resume_builder_api',
            userId: session.user.email
          });

          return NextResponse.json({
            success: true,
            message: 'Resume deleted successfully'
          });

        } catch (error) {
          const totalDuration = Date.now() - startTime;

          log.error('Error deleting resume', error as Error, {
            component: 'resume_builder_api',
            action: 'delete_resume',
            userId: session.user.email
          });

          trackError.api(error as Error, `/api/resume-builder/${id}`, 'DELETE', 500);

          ErrorReporter.captureError(error as Error, {
            userId: session.user.email,
            userEmail: session.user.email,
            action: 'delete_resume',
            component: 'resume_builder_api'
          });

          log.api('DELETE', `/api/resume-builder/${id}`, 500, totalDuration, {
            component: 'resume_builder_api',
            userId: session.user.email
          });

          return NextResponse.json({
            success: false,
            error: 'Internal Server Error'
          }, { status: 500 });
        }
      }
    );
  });
}
